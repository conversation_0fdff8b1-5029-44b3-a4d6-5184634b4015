﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CinemaBooking.Migrations
{
    /// <inheritdoc />
    public partial class SeedInitialData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OtpInfos_nguoi_dung_NguoiDungId",
                table: "OtpInfos");

            migrationBuilder.DropPrimaryKey(
                name: "PK_OtpInfos",
                table: "OtpInfos");

            migrationBuilder.DropIndex(
                name: "IX_OtpInfos_NguoiDungId",
                table: "OtpInfos");

            migrationBuilder.DropColumn(
                name: "NguoiDungId",
                table: "OtpInfos");

            migrationBuilder.RenameTable(
                name: "OtpInfos",
                newName: "otp_info");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "otp_info",
                newName: "email");

            migrationBuilder.RenameColumn(
                name: "ThoiGian<PERSON>ao",
                table: "otp_info",
                newName: "thoi_gian_tao");

            migrationBuilder.RenameColumn(
                name: "ThoiGianHetHan",
                table: "otp_info",
                newName: "thoi_gian_het_han");

            migrationBuilder.RenameColumn(
                name: "MaXacThuc",
                table: "otp_info",
                newName: "ma_xac_thuc");

            migrationBuilder.RenameColumn(
                name: "MaNguoiDung",
                table: "otp_info",
                newName: "ma_nguoi_dung");

            migrationBuilder.RenameColumn(
                name: "LoaiOtp",
                table: "otp_info",
                newName: "loai_otp");

            migrationBuilder.RenameColumn(
                name: "DaSuDung",
                table: "otp_info",
                newName: "da_su_dung");

            migrationBuilder.RenameColumn(
                name: "MaOtp",
                table: "otp_info",
                newName: "ma_otp");

            migrationBuilder.AddPrimaryKey(
                name: "PK_otp_info",
                table: "otp_info",
                column: "ma_otp");

            migrationBuilder.CreateIndex(
                name: "IX_otp_info_ma_nguoi_dung",
                table: "otp_info",
                column: "ma_nguoi_dung");

            migrationBuilder.AddForeignKey(
                name: "FK_otp_info_nguoi_dung_ma_nguoi_dung",
                table: "otp_info",
                column: "ma_nguoi_dung",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_otp_info_nguoi_dung_ma_nguoi_dung",
                table: "otp_info");

            migrationBuilder.DropPrimaryKey(
                name: "PK_otp_info",
                table: "otp_info");

            migrationBuilder.DropIndex(
                name: "IX_otp_info_ma_nguoi_dung",
                table: "otp_info");

            migrationBuilder.RenameTable(
                name: "otp_info",
                newName: "OtpInfos");

            migrationBuilder.RenameColumn(
                name: "email",
                table: "OtpInfos",
                newName: "Email");

            migrationBuilder.RenameColumn(
                name: "thoi_gian_tao",
                table: "OtpInfos",
                newName: "ThoiGianTao");

            migrationBuilder.RenameColumn(
                name: "thoi_gian_het_han",
                table: "OtpInfos",
                newName: "ThoiGianHetHan");

            migrationBuilder.RenameColumn(
                name: "ma_xac_thuc",
                table: "OtpInfos",
                newName: "MaXacThuc");

            migrationBuilder.RenameColumn(
                name: "ma_nguoi_dung",
                table: "OtpInfos",
                newName: "MaNguoiDung");

            migrationBuilder.RenameColumn(
                name: "loai_otp",
                table: "OtpInfos",
                newName: "LoaiOtp");

            migrationBuilder.RenameColumn(
                name: "da_su_dung",
                table: "OtpInfos",
                newName: "DaSuDung");

            migrationBuilder.RenameColumn(
                name: "ma_otp",
                table: "OtpInfos",
                newName: "MaOtp");

            migrationBuilder.AddColumn<int>(
                name: "NguoiDungId",
                table: "OtpInfos",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddPrimaryKey(
                name: "PK_OtpInfos",
                table: "OtpInfos",
                column: "MaOtp");

            migrationBuilder.CreateIndex(
                name: "IX_OtpInfos_NguoiDungId",
                table: "OtpInfos",
                column: "NguoiDungId");

            migrationBuilder.AddForeignKey(
                name: "FK_OtpInfos_nguoi_dung_NguoiDungId",
                table: "OtpInfos",
                column: "NguoiDungId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
