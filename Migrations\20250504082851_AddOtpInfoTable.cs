﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CinemaBooking.Migrations
{
    /// <inheritdoc />
    public partial class AddOtpInfoTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_identity_role_claims_identity_roles_RoleId",
                table: "identity_role_claims");

            migrationBuilder.DropForeignKey(
                name: "FK_identity_user_claims_nguoi_dung_UserId",
                table: "identity_user_claims");

            migrationBuilder.DropForeignKey(
                name: "FK_identity_user_logins_nguoi_dung_UserId",
                table: "identity_user_logins");

            migrationBuilder.DropForeignKey(
                name: "FK_identity_user_roles_identity_roles_RoleId",
                table: "identity_user_roles");

            migrationBuilder.DropForeignKey(
                name: "FK_identity_user_roles_nguoi_dung_UserId",
                table: "identity_user_roles");

            migrationBuilder.DropForeignKey(
                name: "FK_identity_user_tokens_nguoi_dung_UserId",
                table: "identity_user_tokens");

            migrationBuilder.DropIndex(
                name: "EmailIndex",
                table: "nguoi_dung");

            migrationBuilder.DropIndex(
                name: "UserNameIndex",
                table: "nguoi_dung");

            migrationBuilder.DropPrimaryKey(
                name: "PK_identity_user_tokens",
                table: "identity_user_tokens");

            migrationBuilder.DropPrimaryKey(
                name: "PK_identity_user_roles",
                table: "identity_user_roles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_identity_user_logins",
                table: "identity_user_logins");

            migrationBuilder.DropPrimaryKey(
                name: "PK_identity_user_claims",
                table: "identity_user_claims");

            migrationBuilder.DropPrimaryKey(
                name: "PK_identity_roles",
                table: "identity_roles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_identity_role_claims",
                table: "identity_role_claims");

            migrationBuilder.DropColumn(
                name: "AccessFailedCount",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "ConcurrencyStamp",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "EmailConfirmed",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "LockoutEnabled",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "LockoutEnd",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "NormalizedEmail",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "NormalizedUserName",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "PasswordHash",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "PhoneNumberConfirmed",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "SecurityStamp",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "TwoFactorEnabled",
                table: "nguoi_dung");

            migrationBuilder.RenameTable(
                name: "identity_user_tokens",
                newName: "AspNetUserTokens");

            migrationBuilder.RenameTable(
                name: "identity_user_roles",
                newName: "nguoi_dung_vai_tro");

            migrationBuilder.RenameTable(
                name: "identity_user_logins",
                newName: "AspNetUserLogins");

            migrationBuilder.RenameTable(
                name: "identity_user_claims",
                newName: "AspNetUserClaims");

            migrationBuilder.RenameTable(
                name: "identity_roles",
                newName: "AspNetRoles");

            migrationBuilder.RenameTable(
                name: "identity_role_claims",
                newName: "AspNetRoleClaims");

            migrationBuilder.RenameColumn(
                name: "RoleId",
                table: "nguoi_dung_vai_tro",
                newName: "ma_vai_tro");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "nguoi_dung_vai_tro",
                newName: "ma_nguoi_dung");

            migrationBuilder.RenameIndex(
                name: "IX_identity_user_roles_RoleId",
                table: "nguoi_dung_vai_tro",
                newName: "IX_nguoi_dung_vai_tro_ma_vai_tro");

            migrationBuilder.RenameIndex(
                name: "IX_identity_user_logins_UserId",
                table: "AspNetUserLogins",
                newName: "IX_AspNetUserLogins_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_identity_user_claims_UserId",
                table: "AspNetUserClaims",
                newName: "IX_AspNetUserClaims_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_identity_role_claims_RoleId",
                table: "AspNetRoleClaims",
                newName: "IX_AspNetRoleClaims_RoleId");

            migrationBuilder.AlterColumn<string>(
                name: "so_dien_thoai",
                table: "nguoi_dung",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(15)",
                oldMaxLength: 15);

            migrationBuilder.AlterColumn<string>(
                name: "mat_khau",
                table: "nguoi_dung",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "ho_ten",
                table: "nguoi_dung",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "AspNetRoles",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Discriminator",
                table: "AspNetRoles",
                type: "nvarchar(21)",
                maxLength: 21,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserTokens",
                table: "AspNetUserTokens",
                columns: new[] { "UserId", "LoginProvider", "Name" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_nguoi_dung_vai_tro",
                table: "nguoi_dung_vai_tro",
                columns: new[] { "ma_nguoi_dung", "ma_vai_tro" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserLogins",
                table: "AspNetUserLogins",
                columns: new[] { "LoginProvider", "ProviderKey" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetUserClaims",
                table: "AspNetUserClaims",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetRoles",
                table: "AspNetRoles",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AspNetRoleClaims",
                table: "AspNetRoleClaims",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "khuyen_mai",
                columns: table => new
                {
                    ma_khuyen_mai = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    phan_tram_giam = table.Column<int>(type: "int", nullable: false),
                    ngay_bat_dau = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ngay_ket_thuc = table.Column<DateTime>(type: "datetime2", nullable: false),
                    mo_ta = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_khuyen_mai", x => x.ma_khuyen_mai);
                });

            migrationBuilder.CreateTable(
                name: "OtpInfos",
                columns: table => new
                {
                    MaOtp = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MaXacThuc = table.Column<string>(type: "nvarchar(6)", maxLength: 6, nullable: false),
                    ThoiGianTao = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ThoiGianHetHan = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LoaiOtp = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    DaSuDung = table.Column<bool>(type: "bit", nullable: false),
                    MaNguoiDung = table.Column<int>(type: "int", nullable: true),
                    NguoiDungId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OtpInfos", x => x.MaOtp);
                    table.ForeignKey(
                        name: "FK_OtpInfos_nguoi_dung_NguoiDungId",
                        column: x => x.NguoiDungId,
                        principalTable: "nguoi_dung",
                        principalColumn: "ma_nguoi_dung",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "phim",
                columns: table => new
                {
                    ma_phim = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ten_phim = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    mo_ta = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    thoi_luong = table.Column<int>(type: "int", nullable: false),
                    the_loai = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ngay_phat_hanh = table.Column<DateTime>(type: "datetime2", nullable: true),
                    url_poster = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    dinh_dang = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    trailer = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_phim", x => x.ma_phim);
                });

            migrationBuilder.CreateTable(
                name: "rap_phim",
                columns: table => new
                {
                    ma_rap = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ten_rap = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    dia_chi = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    thanh_pho = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_rap_phim", x => x.ma_rap);
                });

            migrationBuilder.CreateTable(
                name: "danh_gia",
                columns: table => new
                {
                    ma_danh_gia = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_nguoi_dung = table.Column<int>(type: "int", nullable: false),
                    ma_phim = table.Column<int>(type: "int", nullable: false),
                    diem_so = table.Column<int>(type: "int", nullable: true),
                    binh_luan = table.Column<string>(type: "ntext", nullable: false),
                    ngay_danh_gia = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_danh_gia", x => x.ma_danh_gia);
                    table.ForeignKey(
                        name: "FK_danh_gia_nguoi_dung_ma_nguoi_dung",
                        column: x => x.ma_nguoi_dung,
                        principalTable: "nguoi_dung",
                        principalColumn: "ma_nguoi_dung",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_danh_gia_phim_ma_phim",
                        column: x => x.ma_phim,
                        principalTable: "phim",
                        principalColumn: "ma_phim",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ngon_ngu_phim",
                columns: table => new
                {
                    ma_ngon_ngu = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_phim = table.Column<int>(type: "int", nullable: true),
                    ngon_ngu = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    phu_de = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ngon_ngu_phim", x => x.ma_ngon_ngu);
                    table.ForeignKey(
                        name: "FK_ngon_ngu_phim_phim_ma_phim",
                        column: x => x.ma_phim,
                        principalTable: "phim",
                        principalColumn: "ma_phim",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "phong_chieu",
                columns: table => new
                {
                    ma_phong = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_rap = table.Column<int>(type: "int", nullable: false),
                    so_phong = table.Column<int>(type: "int", nullable: false),
                    suc_chua = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_phong_chieu", x => x.ma_phong);
                    table.ForeignKey(
                        name: "FK_phong_chieu_rap_phim_ma_rap",
                        column: x => x.ma_rap,
                        principalTable: "rap_phim",
                        principalColumn: "ma_rap",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ghe",
                columns: table => new
                {
                    ma_ghe = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_phong = table.Column<int>(type: "int", nullable: false),
                    so_ghe = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    loai_ghe = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ghe", x => x.ma_ghe);
                    table.ForeignKey(
                        name: "FK_ghe_phong_chieu_ma_phong",
                        column: x => x.ma_phong,
                        principalTable: "phong_chieu",
                        principalColumn: "ma_phong",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "lich_chieu",
                columns: table => new
                {
                    ma_lich_chieu = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_phim = table.Column<int>(type: "int", nullable: false),
                    ma_phong = table.Column<int>(type: "int", nullable: false),
                    ngay_chieu = table.Column<DateTime>(type: "datetime2", nullable: false),
                    gio_chieu = table.Column<TimeSpan>(type: "time", nullable: false),
                    gia_ve = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    ma_ngon_ngu = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_lich_chieu", x => x.ma_lich_chieu);
                    table.ForeignKey(
                        name: "FK_lich_chieu_ngon_ngu_phim_ma_ngon_ngu",
                        column: x => x.ma_ngon_ngu,
                        principalTable: "ngon_ngu_phim",
                        principalColumn: "ma_ngon_ngu",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_lich_chieu_phim_ma_phim",
                        column: x => x.ma_phim,
                        principalTable: "phim",
                        principalColumn: "ma_phim",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_lich_chieu_phong_chieu_ma_phong",
                        column: x => x.ma_phong,
                        principalTable: "phong_chieu",
                        principalColumn: "ma_phong",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dat_ve",
                columns: table => new
                {
                    ma_dat_ve = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_nguoi_dung = table.Column<int>(type: "int", nullable: false),
                    ma_lich_chieu = table.Column<int>(type: "int", nullable: false),
                    ngay_dat = table.Column<DateTime>(type: "datetime2", nullable: true),
                    tong_tien = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    trang_thai = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ma_khuyen_mai = table.Column<int>(type: "int", nullable: true),
                    ghi_chu = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dat_ve", x => x.ma_dat_ve);
                    table.ForeignKey(
                        name: "FK_dat_ve_khuyen_mai_ma_khuyen_mai",
                        column: x => x.ma_khuyen_mai,
                        principalTable: "khuyen_mai",
                        principalColumn: "ma_khuyen_mai");
                    table.ForeignKey(
                        name: "FK_dat_ve_lich_chieu_ma_lich_chieu",
                        column: x => x.ma_lich_chieu,
                        principalTable: "lich_chieu",
                        principalColumn: "ma_lich_chieu",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_dat_ve_nguoi_dung_ma_nguoi_dung",
                        column: x => x.ma_nguoi_dung,
                        principalTable: "nguoi_dung",
                        principalColumn: "ma_nguoi_dung",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dat_ve_ghe",
                columns: table => new
                {
                    ma_dat_ve = table.Column<int>(type: "int", nullable: false),
                    ma_ghe = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dat_ve_ghe", x => new { x.ma_dat_ve, x.ma_ghe });
                    table.ForeignKey(
                        name: "FK_dat_ve_ghe_dat_ve_ma_dat_ve",
                        column: x => x.ma_dat_ve,
                        principalTable: "dat_ve",
                        principalColumn: "ma_dat_ve",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_dat_ve_ghe_ghe_ma_ghe",
                        column: x => x.ma_ghe,
                        principalTable: "ghe",
                        principalColumn: "ma_ghe",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "thanh_toan",
                columns: table => new
                {
                    ma_thanh_toan = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_dat_ve = table.Column<int>(type: "int", nullable: false),
                    so_tien = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    phuong_thuc_thanh_toan = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    trang_thai = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ma_giao_dich = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ma_giao_dich_ngan_hang = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ngay_thanh_toan = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ghi_chu = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_thanh_toan", x => x.ma_thanh_toan);
                    table.ForeignKey(
                        name: "FK_thanh_toan_dat_ve_ma_dat_ve",
                        column: x => x.ma_dat_ve,
                        principalTable: "dat_ve",
                        principalColumn: "ma_dat_ve",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "lich_su_giao_dich",
                columns: table => new
                {
                    ma_giao_dich = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ma_nguoi_dung = table.Column<int>(type: "int", nullable: true),
                    ma_thanh_toan = table.Column<int>(type: "int", nullable: true),
                    loai_giao_dich = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    trang_thai = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    noi_dung = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ngay_giao_dich = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_lich_su_giao_dich", x => x.ma_giao_dich);
                    table.ForeignKey(
                        name: "FK_lich_su_giao_dich_nguoi_dung_ma_nguoi_dung",
                        column: x => x.ma_nguoi_dung,
                        principalTable: "nguoi_dung",
                        principalColumn: "ma_nguoi_dung",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_lich_su_giao_dich_thanh_toan_ma_thanh_toan",
                        column: x => x.ma_thanh_toan,
                        principalTable: "thanh_toan",
                        principalColumn: "ma_thanh_toan",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_nguoi_dung_ma_vai_tro",
                table: "nguoi_dung",
                column: "ma_vai_tro");

            migrationBuilder.CreateIndex(
                name: "IX_danh_gia_ma_nguoi_dung",
                table: "danh_gia",
                column: "ma_nguoi_dung");

            migrationBuilder.CreateIndex(
                name: "IX_danh_gia_ma_phim",
                table: "danh_gia",
                column: "ma_phim");

            migrationBuilder.CreateIndex(
                name: "IX_dat_ve_ma_khuyen_mai",
                table: "dat_ve",
                column: "ma_khuyen_mai");

            migrationBuilder.CreateIndex(
                name: "IX_dat_ve_ma_lich_chieu",
                table: "dat_ve",
                column: "ma_lich_chieu");

            migrationBuilder.CreateIndex(
                name: "IX_dat_ve_ma_nguoi_dung",
                table: "dat_ve",
                column: "ma_nguoi_dung");

            migrationBuilder.CreateIndex(
                name: "IX_dat_ve_ghe_ma_ghe",
                table: "dat_ve_ghe",
                column: "ma_ghe");

            migrationBuilder.CreateIndex(
                name: "IX_ghe_ma_phong",
                table: "ghe",
                column: "ma_phong");

            migrationBuilder.CreateIndex(
                name: "IX_lich_chieu_ma_ngon_ngu",
                table: "lich_chieu",
                column: "ma_ngon_ngu");

            migrationBuilder.CreateIndex(
                name: "IX_lich_chieu_ma_phim",
                table: "lich_chieu",
                column: "ma_phim");

            migrationBuilder.CreateIndex(
                name: "IX_lich_chieu_ma_phong",
                table: "lich_chieu",
                column: "ma_phong");

            migrationBuilder.CreateIndex(
                name: "IX_lich_su_giao_dich_ma_nguoi_dung",
                table: "lich_su_giao_dich",
                column: "ma_nguoi_dung");

            migrationBuilder.CreateIndex(
                name: "IX_lich_su_giao_dich_ma_thanh_toan",
                table: "lich_su_giao_dich",
                column: "ma_thanh_toan");

            migrationBuilder.CreateIndex(
                name: "IX_ngon_ngu_phim_ma_phim",
                table: "ngon_ngu_phim",
                column: "ma_phim");

            migrationBuilder.CreateIndex(
                name: "IX_OtpInfos_NguoiDungId",
                table: "OtpInfos",
                column: "NguoiDungId");

            migrationBuilder.CreateIndex(
                name: "IX_phong_chieu_ma_rap",
                table: "phong_chieu",
                column: "ma_rap");

            migrationBuilder.CreateIndex(
                name: "IX_thanh_toan_ma_dat_ve",
                table: "thanh_toan",
                column: "ma_dat_ve");

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserClaims_nguoi_dung_UserId",
                table: "AspNetUserClaims",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserLogins_nguoi_dung_UserId",
                table: "AspNetUserLogins",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserTokens_nguoi_dung_UserId",
                table: "AspNetUserTokens",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_nguoi_dung_AspNetRoles_ma_vai_tro",
                table: "nguoi_dung",
                column: "ma_vai_tro",
                principalTable: "AspNetRoles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_nguoi_dung_vai_tro_AspNetRoles_ma_vai_tro",
                table: "nguoi_dung_vai_tro",
                column: "ma_vai_tro",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_nguoi_dung_vai_tro_nguoi_dung_ma_nguoi_dung",
                table: "nguoi_dung_vai_tro",
                column: "ma_nguoi_dung",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                table: "AspNetRoleClaims");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserClaims_nguoi_dung_UserId",
                table: "AspNetUserClaims");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserLogins_nguoi_dung_UserId",
                table: "AspNetUserLogins");

            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserTokens_nguoi_dung_UserId",
                table: "AspNetUserTokens");

            migrationBuilder.DropForeignKey(
                name: "FK_nguoi_dung_AspNetRoles_ma_vai_tro",
                table: "nguoi_dung");

            migrationBuilder.DropForeignKey(
                name: "FK_nguoi_dung_vai_tro_AspNetRoles_ma_vai_tro",
                table: "nguoi_dung_vai_tro");

            migrationBuilder.DropForeignKey(
                name: "FK_nguoi_dung_vai_tro_nguoi_dung_ma_nguoi_dung",
                table: "nguoi_dung_vai_tro");

            migrationBuilder.DropTable(
                name: "danh_gia");

            migrationBuilder.DropTable(
                name: "dat_ve_ghe");

            migrationBuilder.DropTable(
                name: "lich_su_giao_dich");

            migrationBuilder.DropTable(
                name: "OtpInfos");

            migrationBuilder.DropTable(
                name: "ghe");

            migrationBuilder.DropTable(
                name: "thanh_toan");

            migrationBuilder.DropTable(
                name: "dat_ve");

            migrationBuilder.DropTable(
                name: "khuyen_mai");

            migrationBuilder.DropTable(
                name: "lich_chieu");

            migrationBuilder.DropTable(
                name: "ngon_ngu_phim");

            migrationBuilder.DropTable(
                name: "phong_chieu");

            migrationBuilder.DropTable(
                name: "phim");

            migrationBuilder.DropTable(
                name: "rap_phim");

            migrationBuilder.DropIndex(
                name: "IX_nguoi_dung_ma_vai_tro",
                table: "nguoi_dung");

            migrationBuilder.DropPrimaryKey(
                name: "PK_nguoi_dung_vai_tro",
                table: "nguoi_dung_vai_tro");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserTokens",
                table: "AspNetUserTokens");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserLogins",
                table: "AspNetUserLogins");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetUserClaims",
                table: "AspNetUserClaims");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetRoles",
                table: "AspNetRoles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AspNetRoleClaims",
                table: "AspNetRoleClaims");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "Discriminator",
                table: "AspNetRoles");

            migrationBuilder.RenameTable(
                name: "nguoi_dung_vai_tro",
                newName: "identity_user_roles");

            migrationBuilder.RenameTable(
                name: "AspNetUserTokens",
                newName: "identity_user_tokens");

            migrationBuilder.RenameTable(
                name: "AspNetUserLogins",
                newName: "identity_user_logins");

            migrationBuilder.RenameTable(
                name: "AspNetUserClaims",
                newName: "identity_user_claims");

            migrationBuilder.RenameTable(
                name: "AspNetRoles",
                newName: "identity_roles");

            migrationBuilder.RenameTable(
                name: "AspNetRoleClaims",
                newName: "identity_role_claims");

            migrationBuilder.RenameColumn(
                name: "ma_vai_tro",
                table: "identity_user_roles",
                newName: "RoleId");

            migrationBuilder.RenameColumn(
                name: "ma_nguoi_dung",
                table: "identity_user_roles",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "IX_nguoi_dung_vai_tro_ma_vai_tro",
                table: "identity_user_roles",
                newName: "IX_identity_user_roles_RoleId");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "identity_user_logins",
                newName: "IX_identity_user_logins_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "identity_user_claims",
                newName: "IX_identity_user_claims_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "identity_role_claims",
                newName: "IX_identity_role_claims_RoleId");

            migrationBuilder.AlterColumn<string>(
                name: "so_dien_thoai",
                table: "nguoi_dung",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(15)",
                oldMaxLength: 15,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "mat_khau",
                table: "nguoi_dung",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ho_ten",
                table: "nguoi_dung",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AccessFailedCount",
                table: "nguoi_dung",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "nguoi_dung",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "EmailConfirmed",
                table: "nguoi_dung",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "LockoutEnabled",
                table: "nguoi_dung",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "LockoutEnd",
                table: "nguoi_dung",
                type: "datetimeoffset",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NormalizedEmail",
                table: "nguoi_dung",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NormalizedUserName",
                table: "nguoi_dung",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PasswordHash",
                table: "nguoi_dung",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "PhoneNumberConfirmed",
                table: "nguoi_dung",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "SecurityStamp",
                table: "nguoi_dung",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "TwoFactorEnabled",
                table: "nguoi_dung",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddPrimaryKey(
                name: "PK_identity_user_roles",
                table: "identity_user_roles",
                columns: new[] { "UserId", "RoleId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_identity_user_tokens",
                table: "identity_user_tokens",
                columns: new[] { "UserId", "LoginProvider", "Name" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_identity_user_logins",
                table: "identity_user_logins",
                columns: new[] { "LoginProvider", "ProviderKey" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_identity_user_claims",
                table: "identity_user_claims",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_identity_roles",
                table: "identity_roles",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_identity_role_claims",
                table: "identity_role_claims",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                table: "nguoi_dung",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                table: "nguoi_dung",
                column: "NormalizedUserName",
                unique: true,
                filter: "[NormalizedUserName] IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_identity_role_claims_identity_roles_RoleId",
                table: "identity_role_claims",
                column: "RoleId",
                principalTable: "identity_roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_identity_user_claims_nguoi_dung_UserId",
                table: "identity_user_claims",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_identity_user_logins_nguoi_dung_UserId",
                table: "identity_user_logins",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_identity_user_roles_identity_roles_RoleId",
                table: "identity_user_roles",
                column: "RoleId",
                principalTable: "identity_roles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_identity_user_roles_nguoi_dung_UserId",
                table: "identity_user_roles",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_identity_user_tokens_nguoi_dung_UserId",
                table: "identity_user_tokens",
                column: "UserId",
                principalTable: "nguoi_dung",
                principalColumn: "ma_nguoi_dung",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
