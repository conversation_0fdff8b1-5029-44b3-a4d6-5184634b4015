@model CinemaBooking.Models.ViewModels.PhimDanhGiaViewModel

@{
    ViewData["Title"] = Model.Phim.TenPhim;
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-4">
            <img src="@(string.IsNullOrEmpty(Model.Phim.UrlPoster) ? "/images/no-image.jpg" : Model.Phim.UrlPoster)" class="img-fluid rounded shadow" alt="@Model.Phim.TenPhim">
            
            @if (!string.IsNullOrEmpty(Model.Phim.Trailer))
            {
                <div class="mt-3">
                    <h5>Trailer:</h5>
                    <div class="ratio ratio-16x9">
                        <video controls class="rounded shadow">
                            <source src="@Model.Phim.Trailer" type="video/mp4">
                            Trình duyệt của bạn không hỗ trợ video.
                        </video>
                    </div>
                </div>
            }
            
            <!-- Partial View Đánh Giá -->
            <div class="mt-4">
                @await Html.PartialAsync("_DanhGiaPartial", Model)
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-body">
                    <h1 class="card-title">@Model.Phim.TenPhim</h1>
                    
                    <div class="mb-3">
                        <span class="badge bg-primary">@Model.Phim.TheLoai</span>
                        <span class="badge bg-info">@Model.Phim.DinhDang</span>
                        <span class="badge bg-secondary">@Model.Phim.ThoiLuong phút</span>
                    </div>

                    <div class="mb-3">
                        <h5>Đánh giá:</h5>
                        <div class="d-flex align-items-center">
                            <div class="rating">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Math.Floor(Model.DiemTrungBinh))
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                    else if (i - 0.5 <= Model.DiemTrungBinh)
                                    {
                                        <i class="fas fa-star-half-alt text-warning"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star text-warning"></i>
                                    }
                                }
                            </div>
                            <span class="ms-2">(@Model.TongSoDanhGia đánh giá)</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Thông tin:</h5>
                        <p><strong>Ngày phát hành:</strong> @(Model.Phim.NgayPhatHanh?.ToString("dd/MM/yyyy") ?? "Chưa cập nhật")</p>
                        
                        @if (Model.Phim.NgonNguPhims != null && Model.Phim.NgonNguPhims.Any())
                        {
                            <p><strong>Ngôn ngữ:</strong> 
                                @string.Join(", ", Model.Phim.NgonNguPhims.Select(n => n.NgonNgu))
                            </p>
                        }
                    </div>
                    
                    <div class="mb-3">
                        <h5>Mô tả:</h5>
                        <p class="text-justify">@(string.IsNullOrEmpty(Model.Phim.MoTa) ? "Chưa có mô tả" : Model.Phim.MoTa)</p>
                    </div>

                    <div class="mb-3">
                        <h5>Đánh giá gần đây:</h5>
                        @if (Model.DanhSachDanhGia.Any())
                        {
                            <div class="recent-reviews">
                                @foreach (var danhGia in Model.DanhSachDanhGia.Take(3))
                                {
                                    <div class="card mb-2">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">@danhGia.NguoiDung.HoTen</h6>
                                                <div class="rating">
                                                    @for (int i = 1; i <= 5; i++)
                                                    {
                                                        if (i <= danhGia.DiemSo)
                                                        {
                                                            <i class="fas fa-star text-warning"></i>
                                                        }
                                                        else
                                                        {
                                                            <i class="far fa-star text-warning"></i>
                                                        }
                                                    }
                                                </div>
                                            </div>
                                            <small class="text-muted">@danhGia.NgayDanhGia?.ToString("dd/MM/yyyy")</small>
                                            <p class="mt-2 mb-0">@danhGia.BinhLuan</p>
                                        </div>
                                    </div>
                                }
                                <div class="text-end mt-2">
                                    <a asp-controller="DanhGia" asp-action="DanhSachDanhGia" asp-route-id="@Model.Phim.MaPhim" 
                                       class="btn btn-outline-primary btn-sm">
                                        Xem tất cả đánh giá
                                    </a>
                                </div>
                            </div>
                        }
                        else
                        {
                            <p>Chưa có đánh giá nào. 
                                <a asp-controller="DanhGia" asp-action="CreateDanhGia" asp-route-id="@Model.Phim.MaPhim">
                                    Viết đánh giá đầu tiên!
                                </a>
                            </p>
                        }
                    </div>

                    <div class="mt-4">
                        @if (ViewBag.HasFutureScreenings)
                        {
                            <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@Model.Phim.MaPhim" class="btn btn-primary btn-lg">
                                <i class="fas fa-ticket-alt me-2"></i>Đặt vé
                            </a>
                        }
                        else
                        {
                            <button class="btn btn-secondary btn-lg" disabled>
                                <i class="fas fa-ticket-alt me-2"></i>Chưa có lịch chiếu
                            </button>
                        }
                        <a asp-controller="DanhGia" asp-action="CreateDanhGia" asp-route-id="@Model.Phim.MaPhim" class="btn btn-success">
                            <i class="fas fa-star me-2"></i>Đánh giá
                        </a>
                        <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Load FontAwesome if not already loaded
            if (!$('link[href*="fontawesome"]').length) {
                $('head').append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">');
            }
        });
    </script>
}

@section Styles {
    <style>
        .rating {
            font-size: 1.2rem;
        }
        .card {
            border: none;
            border-radius: 10px;
        }
        .text-justify {
            text-align: justify;
        }
        .recent-reviews .card {
            border: 1px solid rgba(0,0,0,.125);
        }
    </style>
} 