﻿@model IEnumerable<CinemaBooking.Models.Phim>

@{
    ViewData["Title"] = "Trang chủ";
}

<style>
    .movie-container {
        margin-bottom: 30px;
        padding: 0 10px;
        position: relative;
        transition: transform 0.3s ease;
        z-index: 1;
    }
    
    .movie-container:hover {
        z-index: 10;
    }
    
    .movie-card {
        transition: all 0.3s ease;
        position: relative;
        cursor: pointer;
        height: 350px;
        overflow: hidden;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        background-color: #000;
        width: 100%;
    }
    
    .movie-container::before {
        content: '';
        position: absolute;
        top: -40px;
        left: -40px;
        right: -40px;
        bottom: -40px;
        z-index: -1;
        pointer-events: none;
    }
    
    .movie-card:hover {
        box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    }
    
    .movie-container.push-left {
        transform: translateX(-20px);
        transition: transform 0.4s ease;
    }
    
    .movie-container.push-right {
        transform: translateX(20px);
        transition: transform 0.4s ease;
    }
    
    .col-md-3:nth-child(4n-3) .movie-card:hover {
        transform: scale(1.08) translateX(10px);
    }
    
    .col-md-3:nth-child(4n) .movie-card:hover {
        transform: scale(1.08) translateX(-10px);
    }
    
    .card-img-top {
        height: 350px;
        width: 100%;
        object-fit: cover;
    }
    
    .movie-trailer-container {
        display: none;
    }
    
    .card-content {
        padding: 15px;
        background: linear-gradient(to top, rgba(0,0,0,1), rgba(0,0,0,0.8), rgba(0,0,0,0));
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        color: white;
        z-index: 2;
    }
    
    .card-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
        text-shadow: 1px 1px 2px #000;
    }
    
    .movie-genre {
        display: inline-block;
        background-color: rgba(229, 9, 20, 0.8);
        padding: 2px 8px;
        border-radius: 4px;
        margin-bottom: 5px;
        font-size: 12px;
    }
    
    .btn-detail {
        background-color: #e50914;
        border: none;
        margin-top: 8px;
        transition: all 0.3s;
        font-size: 14px;
        padding: 5px 15px;
    }
    
    .btn-detail:hover {
        background-color: #f40612;
        transform: translateY(-2px);
    }
    
    .movie-section {
        padding: 20px 0;
    }
    
    .section-title {
        margin-bottom: 25px;
        font-weight: 600;
        position: relative;
        display: inline-block;
    }
    
    .section-title:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -8px;
        height: 3px;
        width: 50px;
        background-color: #e50914;
    }
    
    body {
        background-attachment: fixed;
    }
    
    .movie-container {
        z-index: 2;
    }
    
    .movie-section {
        background-color: rgba(11, 20, 30, 0.85);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        margin-top: 20px;
        color: #fff;
    }
    
    .hero-section {
        background-color: rgba(0, 0, 0, 0.75);
        border-radius: 12px;
        overflow: hidden;
        margin: 0;
        padding: 0;
        position: relative;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
    }
    
    .section-title {
        color: #fff;
        margin-bottom: 25px;
        font-weight: 600;
        position: relative;
        display: inline-block;
    }

    /* Carousel styling */
    .carousel-control-prev, .carousel-control-next {
        width: 5%;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        height: 50px;
        width: 50px;
        top: 50%;
        transform: translateY(-50%);
    }

    .carousel-control-prev {
        left: -25px;
    }

    .carousel-control-next {
        right: -25px;
    }

    .carousel-item {
        padding: 10px 20px;
    }

    .carousel-inner {
        overflow: visible;
    }

    /* CSS cho carousel kiểu mới */
    .movie-carousel-container {
        position: relative;
        overflow: hidden;
        padding: 0 50px;
    }
    
    .movie-carousel-wrapper {
        overflow: hidden;
        margin: 0 auto;
        width: 100%;
    }
    
    .movie-carousel-track {
        display: flex;
        transition: transform 0.5s ease;
        will-change: transform;
        backface-visibility: hidden;
    }
    
    .movie-slide {
        flex: 0 0 25%; /* Hiển thị 4 phim mỗi lần */
        max-width: 25%;
        padding: 0 10px;
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .movie-slide.clone {
        /* Style cho các slide clone nếu cần */
    }
    
    /* Hiệu ứng cho slide đang chuyển đổi */
    .movie-carousel-track.transitioning .movie-slide {
        transition: transform 0.5s ease, opacity 0.5s ease;
    }
    
    .carousel-control-prev, .carousel-control-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        border: none;
        transition: all 0.3s ease;
    }
    
    .carousel-control-prev:hover, .carousel-control-next:hover {
        background-color: rgba(229, 9, 20, 0.8);
    }
    
    .carousel-control-prev {
        left: 0;
    }
    
    .carousel-control-next {
        right: 0;
    }
    
    /* Responsive */
    @@media (max-width: 992px) {
        .movie-slide {
            flex: 0 0 33.333%; /* Hiển thị 3 phim trên tablet */
            max-width: 33.333%;
        }
    }
    
    @@media (max-width: 768px) {
        .movie-slide {
            flex: 0 0 50%; /* Hiển thị 2 phim trên mobile lớn */
            max-width: 50%;
        }
    }
    
    @@media (max-width: 576px) {
        .movie-slide {
            flex: 0 0 100%; /* Hiển thị 1 phim trên mobile nhỏ */
            max-width: 100%;
        }
    }
</style>

<div class="container mb-5">
    <div class="hero-section">
        <div style="position: relative; z-index: 10; padding: 60px 0;">
            <div class="text-center">
                <img src="~/images/imagescinezore-logo.png" alt="CineZore" style="max-width: 250px; margin-bottom: 20px; filter: drop-shadow(0 0 15px rgba(255, 0, 0, 0.3));">
                <h2 class="text-white" style="text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); font-size: 2.5rem; font-weight: 700; margin-bottom: 20px;">Trải nghiệm điện ảnh tuyệt vời</h2>
                <p class="text-light" style="text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8); font-size: 1.2rem; margin-bottom: 30px;">Đặt vé xem phim ngay hôm nay</p>
                <a href="#movie-list" class="btn btn-danger btn-lg" style="background-color: #e50914; border: none; padding: 10px 30px; font-weight: 600; box-shadow: 0 4px 8px rgba(229, 9, 20, 0.5); transition: all 0.3s ease;">ĐẶT VÉ NGAY</a>
            </div>
        </div>
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(to bottom, rgba(0,0,0,0.5), rgba(0,0,0,0.3)); z-index: 1;"></div>
    </div>
</div>

<div id="movie-list" class="container movie-section">
    <h1 class="section-title">Phim mới nhất</h1>

    <div class="movie-carousel-container position-relative">
        <!-- Nút điều hướng trái -->
        <button class="carousel-control-prev" id="prevBtn" type="button">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        
        <!-- Vùng hiển thị phim -->
        <div class="movie-carousel-wrapper">
            <div class="movie-carousel-track">
                @foreach (var item in Model)
                {
                    <div class="movie-slide">
                        <div class="movie-container">
                            <div class="movie-card" data-id="@item.MaPhim">
                                <img src="@(string.IsNullOrEmpty(item.UrlPoster) ? "/images/no-image.jpg" : item.UrlPoster)" class="card-img-top" alt="@item.TenPhim">
                                <div class="card-content">
                                    <h5 class="card-title">@item.TenPhim</h5>
                                    <span class="movie-genre">@item.TheLoai</span>
                                    <p class="card-text"><small>@item.ThoiLuong phút</small></p>
                                    <a asp-controller="Phim" asp-action="Detail" asp-route-id="@item.MaPhim" class="btn btn-danger btn-detail">Xem chi tiết</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
        
        <!-- Nút điều hướng phải -->
        <button class="carousel-control-next" id="nextBtn" type="button">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const containers = document.querySelectorAll('.movie-container');
        const track = document.querySelector('.movie-carousel-track');
        const slides = document.querySelectorAll('.movie-slide');
        const prevButton = document.getElementById('prevBtn');
        const nextButton = document.getElementById('nextBtn');
        
        // Phim hiển thị mỗi lần (tùy theo kích thước màn hình)
        const slidesToShow = window.innerWidth < 768 
            ? (window.innerWidth < 576 ? 1 : 2) 
            : (window.innerWidth < 992 ? 3 : 4);
            
        const totalSlides = slides.length;
        let slideWidth = 100 / slidesToShow; // Chiều rộng mỗi slide (%)
        let currentIndex = 0;
        let isTransitioning = false;
        
        // Clone các slide để tạo hiệu ứng vô hạn
        function setupInfiniteCarousel() {
            // Xóa các clone cũ nếu có
            document.querySelectorAll('.movie-slide.clone').forEach(clone => clone.remove());
            
            // Clone các slide đầu để thêm vào cuối
            for (let i = 0; i < slidesToShow; i++) {
                const clone = slides[i].cloneNode(true);
                clone.classList.add('clone');
                track.appendChild(clone);
            }
            
            // Clone các slide cuối để thêm vào đầu
            for (let i = totalSlides - 1; i >= totalSlides - slidesToShow; i--) {
                const clone = slides[i].cloneNode(true);
                clone.classList.add('clone');
                track.insertBefore(clone, track.firstChild);
            }
            
            // Thiết lập vị trí ban đầu
            resetPosition();
        }
        
        // Đặt lại vị trí carousel về ban đầu (không animation)
        function resetPosition() {
            // Tắt transition
            track.style.transition = 'none';
            
            // Tính toán vị trí cần reset
            const adjustedIndex = ((currentIndex % totalSlides) + totalSlides) % totalSlides;
            const offsetPosition = (slidesToShow + adjustedIndex) * slideWidth;
            
            // Đặt lại vị trí
            track.style.transform = `translateX(-${offsetPosition}%)`;
            
            // Force reflow để đảm bảo transition bị vô hiệu hóa
            track.offsetHeight;
            
            // Bật lại transition
            track.style.transition = 'transform 0.5s ease';
        }
        
        // Cập nhật vị trí carousel với hiệu ứng mượt
        function updateCarouselPosition(instant = false) {
            // Tính toán vị trí mới (với offset từ các slide clone)
            const offsetPosition = (slidesToShow + currentIndex) * slideWidth;
            
            // Thêm hoặc bỏ transition tùy vào tham số instant
            if (instant) {
                track.style.transition = 'none';
                track.classList.remove('transitioning');
            } else {
                track.style.transition = 'transform 0.5s ease';
                track.classList.add('transitioning');
                isTransitioning = true;
            }
            
            // Di chuyển đến vị trí mới
            track.style.transform = `translateX(-${offsetPosition}%)`;
            
            // Xử lý kết thúc transition
            if (!instant) {
                track.addEventListener('transitionend', handleTransitionEnd, { once: true });
            }
        }
        
        // Xử lý sự kiện khi transition kết thúc (để xử lý infinite scroll)
        function handleTransitionEnd() {
            isTransitioning = false;
            track.classList.remove('transitioning');
            
            // Reset vị trí nếu đã đi quá giới hạn
            if (currentIndex >= totalSlides) {
                currentIndex = currentIndex % totalSlides;
                resetPosition();
                updateCarouselPosition(true);
            } else if (currentIndex < 0) {
                currentIndex = totalSlides + (currentIndex % totalSlides);
                resetPosition();
                updateCarouselPosition(true);
            }
        }
        
        // Xử lý nút Next
        nextButton.addEventListener('click', () => {
            if (isTransitioning) return;
            currentIndex++;
            updateCarouselPosition();
        });
        
        // Xử lý nút Prev
        prevButton.addEventListener('click', () => {
            if (isTransitioning) return;
            currentIndex--;
            updateCarouselPosition();
        });
        
        // Khởi tạo carousel
        setupInfiniteCarousel();
        
        // Khởi tạo vị trí ban đầu
        updateCarouselPosition(true);
        
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 70, // Offset for navbar
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Thêm xử lý touch swipe cho mobile
        let touchStartX = 0;
        let touchEndX = 0;
        
        const carouselWrapper = document.querySelector('.movie-carousel-wrapper');
        
        carouselWrapper.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });
        
        carouselWrapper.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, { passive: true });
        
        function handleSwipe() {
            const swipeThreshold = 50; // Ngưỡng để xác định swipe
            
            if (touchEndX < touchStartX - swipeThreshold) {
                // Swipe trái -> Next slide
                if (!isTransitioning) {
                    currentIndex++;
                    updateCarouselPosition();
                }
            }
            
            if (touchEndX > touchStartX + swipeThreshold) {
                // Swipe phải -> Previous slide
                if (!isTransitioning) {
                    currentIndex--;
                    updateCarouselPosition();
                }
            }
        }
    });
</script>