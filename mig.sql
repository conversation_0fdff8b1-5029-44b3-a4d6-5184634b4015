USE [cinema_booking]
GO
/****** Object:  Table [dbo].[__EFMigrationsHistory]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[__EFMigrationsHistory](
	[MigrationId] [nvarchar](150) NOT NULL,
	[ProductVersion] [nvarchar](32) NOT NULL,
 CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY CLUSTERED 
(
	[MigrationId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[danh_gia]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[danh_gia](
	[ma_danh_gia] [int] IDENTITY(1,1) NOT NULL,
	[ma_nguoi_dung] [int] NOT NULL,
	[ma_phim] [int] NOT NULL,
	[diem_so] [int] NULL,
	[ngay_danh_gia] [datetime] NULL,
	[binh_luan] [ntext] NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_danh_gia] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[dat_ve]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[dat_ve](
	[ma_dat_ve] [int] IDENTITY(1,1) NOT NULL,
	[ma_nguoi_dung] [int] NOT NULL,
	[ma_lich_chieu] [int] NOT NULL,
	[ngay_dat] [datetime] NULL,
	[tong_tien] [decimal](10, 2) NOT NULL,
	[trang_thai] [nvarchar](50) NULL,
	[ma_khuyen_mai] [int] NULL,
	[ghi_chu] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_dat_ve] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[dat_ve_ghe]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[dat_ve_ghe](
	[ma_dat_ve] [int] NOT NULL,
	[ma_ghe] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_dat_ve] ASC,
	[ma_ghe] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ghe]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ghe](
	[ma_ghe] [int] IDENTITY(1,1) NOT NULL,
	[ma_phong] [int] NOT NULL,
	[so_ghe] [varchar](10) NOT NULL,
	[loai_ghe] [nvarchar](20) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_ghe] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[khuyen_mai]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[khuyen_mai](
	[ma_khuyen_mai] [int] IDENTITY(1,1) NOT NULL,
	[ma_code] [varchar](20) NOT NULL,
	[phan_tram_giam] [int] NOT NULL,
	[ngay_bat_dau] [date] NOT NULL,
	[ngay_ket_thuc] [date] NOT NULL,
	[mo_ta] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_khuyen_mai] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[lich_chieu]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[lich_chieu](
	[ma_lich_chieu] [int] IDENTITY(1,1) NOT NULL,
	[ma_phim] [int] NOT NULL,
	[ma_phong] [int] NOT NULL,
	[ngay_chieu] [date] NOT NULL,
	[gio_chieu] [time](0) NOT NULL,
	[gia_ve] [decimal](10, 2) NOT NULL,
	[ma_ngon_ngu] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_lich_chieu] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[lich_su_giao_dich]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[lich_su_giao_dich](
	[ma_giao_dich] [int] IDENTITY(1,1) NOT NULL,
	[ma_nguoi_dung] [int] NULL,
	[loai_giao_dich] [varchar](50) NOT NULL,
	[noi_dung] [varchar](255) NULL,
	[ngay_giao_dich] [datetime] NULL,
	[ma_thanh_toan] [int] NULL,
	[trang_thai] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_giao_dich] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ngon_ngu_phim]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ngon_ngu_phim](
	[ma_ngon_ngu] [int] IDENTITY(1,1) NOT NULL,
	[ngon_ngu] [nvarchar](50) NULL,
	[phu_de] [nvarchar](50) NULL,
	[ma_phim] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_ngon_ngu] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[nguoi_dung]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[nguoi_dung](
	[ma_nguoi_dung] [int] IDENTITY(1,1) NOT NULL,
	[ten_dang_nhap] [varchar](50) NOT NULL,
	[mat_khau] [varchar](255) NOT NULL,
	[email] [varchar](100) NOT NULL,
	[ho_ten] [varchar](100) NULL,
	[so_dien_thoai] [varchar](15) NULL,
	[ngay_tao] [datetime] NULL,
	[ma_vai_tro] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_nguoi_dung] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[otp_info]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[otp_info](
	[ma_otp] [int] IDENTITY(1,1) NOT NULL,
	[email] [nvarchar](100) NOT NULL,
	[ma_xac_thuc] [nvarchar](6) NOT NULL,
	[thoi_gian_tao] [datetime2](7) NOT NULL,
	[thoi_gian_het_han] [datetime2](7) NOT NULL,
	[loai_otp] [nvarchar](20) NOT NULL,
	[da_su_dung] [bit] NOT NULL,
	[ma_nguoi_dung] [int] NULL,
 CONSTRAINT [PK_otp_info] PRIMARY KEY CLUSTERED 
(
	[ma_otp] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[phim]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[phim](
	[ma_phim] [int] IDENTITY(1,1) NOT NULL,
	[ten_phim] [varchar](100) NOT NULL,
	[mo_ta] [text] NULL,
	[thoi_luong] [int] NOT NULL,
	[the_loai] [varchar](50) NULL,
	[ngay_phat_hanh] [date] NULL,
	[url_poster] [varchar](255) NULL,
	[dinh_dang] [varchar](20) NULL,
	[trailer] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_phim] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[phong_chieu]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[phong_chieu](
	[ma_phong] [int] IDENTITY(1,1) NOT NULL,
	[ma_rap] [int] NOT NULL,
	[so_phong] [int] NOT NULL,
	[suc_chua] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_phong] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[rap_phim]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[rap_phim](
	[ma_rap] [int] IDENTITY(1,1) NOT NULL,
	[ten_rap] [nvarchar](100) NULL,
	[dia_chi] [nvarchar](200) NULL,
	[thanh_pho] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_rap] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[thanh_toan]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[thanh_toan](
	[ma_thanh_toan] [int] IDENTITY(1,1) NOT NULL,
	[ma_dat_ve] [int] NOT NULL,
	[so_tien] [decimal](10, 2) NOT NULL,
	[phuong_thuc_thanh_toan] [nvarchar](50) NOT NULL,
	[trang_thai] [nvarchar](50) NULL,
	[ngay_thanh_toan] [datetime] NULL,
	[ma_giao_dich] [nvarchar](100) NULL,
	[ma_giao_dich_ngan_hang] [nvarchar](100) NULL,
	[ghi_chu] [nvarchar](500) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_thanh_toan] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[vai_tro]    Script Date: 29/05/2025 20:45:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[vai_tro](
	[ma_vai_tro] [int] IDENTITY(1,1) NOT NULL,
	[ten_vai_tro] [varchar](50) NOT NULL,
	[mo_ta] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[ma_vai_tro] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[danh_gia] ADD  DEFAULT (getdate()) FOR [ngay_danh_gia]
GO
ALTER TABLE [dbo].[dat_ve] ADD  DEFAULT (getdate()) FOR [ngay_dat]
GO
ALTER TABLE [dbo].[ghe] ADD  DEFAULT (N'Thường') FOR [loai_ghe]
GO
ALTER TABLE [dbo].[lich_su_giao_dich] ADD  DEFAULT (getdate()) FOR [ngay_giao_dich]
GO
ALTER TABLE [dbo].[nguoi_dung] ADD  DEFAULT (getdate()) FOR [ngay_tao]
GO
ALTER TABLE [dbo].[otp_info] ADD  DEFAULT ((0)) FOR [da_su_dung]
GO
ALTER TABLE [dbo].[thanh_toan] ADD  DEFAULT (getdate()) FOR [ngay_thanh_toan]
GO
ALTER TABLE [dbo].[danh_gia]  WITH CHECK ADD  CONSTRAINT [FK_danh_gia_nguoi_dung] FOREIGN KEY([ma_nguoi_dung])
REFERENCES [dbo].[nguoi_dung] ([ma_nguoi_dung])
GO
ALTER TABLE [dbo].[danh_gia] CHECK CONSTRAINT [FK_danh_gia_nguoi_dung]
GO
ALTER TABLE [dbo].[danh_gia]  WITH CHECK ADD  CONSTRAINT [FK_danh_gia_phim] FOREIGN KEY([ma_phim])
REFERENCES [dbo].[phim] ([ma_phim])
GO
ALTER TABLE [dbo].[danh_gia] CHECK CONSTRAINT [FK_danh_gia_phim]
GO
ALTER TABLE [dbo].[dat_ve]  WITH CHECK ADD  CONSTRAINT [FK_dat_ve_khuyen_mai] FOREIGN KEY([ma_khuyen_mai])
REFERENCES [dbo].[khuyen_mai] ([ma_khuyen_mai])
GO
ALTER TABLE [dbo].[dat_ve] CHECK CONSTRAINT [FK_dat_ve_khuyen_mai]
GO
ALTER TABLE [dbo].[dat_ve]  WITH CHECK ADD  CONSTRAINT [FK_dat_ve_lich_chieu] FOREIGN KEY([ma_lich_chieu])
REFERENCES [dbo].[lich_chieu] ([ma_lich_chieu])
GO
ALTER TABLE [dbo].[dat_ve] CHECK CONSTRAINT [FK_dat_ve_lich_chieu]
GO
ALTER TABLE [dbo].[dat_ve]  WITH CHECK ADD  CONSTRAINT [FK_dat_ve_nguoi_dung] FOREIGN KEY([ma_nguoi_dung])
REFERENCES [dbo].[nguoi_dung] ([ma_nguoi_dung])
GO
ALTER TABLE [dbo].[dat_ve] CHECK CONSTRAINT [FK_dat_ve_nguoi_dung]
GO
ALTER TABLE [dbo].[dat_ve_ghe]  WITH CHECK ADD  CONSTRAINT [FK_dat_ve_ghe_dat_ve] FOREIGN KEY([ma_dat_ve])
REFERENCES [dbo].[dat_ve] ([ma_dat_ve])
GO
ALTER TABLE [dbo].[dat_ve_ghe] CHECK CONSTRAINT [FK_dat_ve_ghe_dat_ve]
GO
ALTER TABLE [dbo].[dat_ve_ghe]  WITH CHECK ADD  CONSTRAINT [FK_dat_ve_ghe_ghe] FOREIGN KEY([ma_ghe])
REFERENCES [dbo].[ghe] ([ma_ghe])
GO
ALTER TABLE [dbo].[dat_ve_ghe] CHECK CONSTRAINT [FK_dat_ve_ghe_ghe]
GO
ALTER TABLE [dbo].[ghe]  WITH CHECK ADD  CONSTRAINT [FK_ghe_phong_chieu] FOREIGN KEY([ma_phong])
REFERENCES [dbo].[phong_chieu] ([ma_phong])
GO
ALTER TABLE [dbo].[ghe] CHECK CONSTRAINT [FK_ghe_phong_chieu]
GO
ALTER TABLE [dbo].[lich_chieu]  WITH CHECK ADD  CONSTRAINT [FK_lich_chieu_ngon_ngu_phim] FOREIGN KEY([ma_ngon_ngu])
REFERENCES [dbo].[ngon_ngu_phim] ([ma_ngon_ngu])
GO
ALTER TABLE [dbo].[lich_chieu] CHECK CONSTRAINT [FK_lich_chieu_ngon_ngu_phim]
GO
ALTER TABLE [dbo].[lich_chieu]  WITH CHECK ADD  CONSTRAINT [FK_lich_chieu_phim] FOREIGN KEY([ma_phim])
REFERENCES [dbo].[phim] ([ma_phim])
GO
ALTER TABLE [dbo].[lich_chieu] CHECK CONSTRAINT [FK_lich_chieu_phim]
GO
ALTER TABLE [dbo].[lich_chieu]  WITH CHECK ADD  CONSTRAINT [FK_lich_chieu_phong_chieu] FOREIGN KEY([ma_phong])
REFERENCES [dbo].[phong_chieu] ([ma_phong])
GO
ALTER TABLE [dbo].[lich_chieu] CHECK CONSTRAINT [FK_lich_chieu_phong_chieu]
GO
ALTER TABLE [dbo].[lich_su_giao_dich]  WITH CHECK ADD  CONSTRAINT [FK_lich_su_giao_dich_nguoi_dung] FOREIGN KEY([ma_nguoi_dung])
REFERENCES [dbo].[nguoi_dung] ([ma_nguoi_dung])
GO
ALTER TABLE [dbo].[lich_su_giao_dich] CHECK CONSTRAINT [FK_lich_su_giao_dich_nguoi_dung]
GO
ALTER TABLE [dbo].[lich_su_giao_dich]  WITH CHECK ADD  CONSTRAINT [FK_lich_su_giao_dich_thanh_toan] FOREIGN KEY([ma_thanh_toan])
REFERENCES [dbo].[thanh_toan] ([ma_thanh_toan])
GO
ALTER TABLE [dbo].[lich_su_giao_dich] CHECK CONSTRAINT [FK_lich_su_giao_dich_thanh_toan]
GO
ALTER TABLE [dbo].[ngon_ngu_phim]  WITH CHECK ADD  CONSTRAINT [FK_ngon_ngu_phim_phim] FOREIGN KEY([ma_phim])
REFERENCES [dbo].[phim] ([ma_phim])
GO
ALTER TABLE [dbo].[ngon_ngu_phim] CHECK CONSTRAINT [FK_ngon_ngu_phim_phim]
GO
ALTER TABLE [dbo].[nguoi_dung]  WITH CHECK ADD  CONSTRAINT [FK_nguoi_dung_vai_tro] FOREIGN KEY([ma_vai_tro])
REFERENCES [dbo].[vai_tro] ([ma_vai_tro])
GO
ALTER TABLE [dbo].[nguoi_dung] CHECK CONSTRAINT [FK_nguoi_dung_vai_tro]
GO
ALTER TABLE [dbo].[otp_info]  WITH CHECK ADD  CONSTRAINT [FK_otp_info_nguoi_dung] FOREIGN KEY([ma_nguoi_dung])
REFERENCES [dbo].[nguoi_dung] ([ma_nguoi_dung])
GO
ALTER TABLE [dbo].[otp_info] CHECK CONSTRAINT [FK_otp_info_nguoi_dung]
GO
ALTER TABLE [dbo].[phong_chieu]  WITH CHECK ADD  CONSTRAINT [FK_phong_chieu_rap_phim] FOREIGN KEY([ma_rap])
REFERENCES [dbo].[rap_phim] ([ma_rap])
GO
ALTER TABLE [dbo].[phong_chieu] CHECK CONSTRAINT [FK_phong_chieu_rap_phim]
GO
ALTER TABLE [dbo].[thanh_toan]  WITH CHECK ADD  CONSTRAINT [FK_thanh_toan_dat_ve] FOREIGN KEY([ma_dat_ve])
REFERENCES [dbo].[dat_ve] ([ma_dat_ve])
GO
ALTER TABLE [dbo].[thanh_toan] CHECK CONSTRAINT [FK_thanh_toan_dat_ve]
GO
