@model CinemaBooking.Models.ViewModels.PhimDanhGiaViewModel

<div class="card shadow-sm mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title mb-0">Đ<PERSON>h giá & <PERSON>hận xét</h5>
            <a asp-controller="DanhGia" asp-action="DanhSachDanhGia" asp-route-id="@Model.Phim.MaPhim" 
               class="btn btn-sm btn-outline-primary">Xem tất cả</a>
        </div>

        <div class="d-flex align-items-center mb-3">
            <div class="display-4 me-3">@Model.DiemTrungBinh</div>
            <div>
                <div class="stars-container mb-2">
                    @for (int i = 1; i <= 5; i++)
                    {
                        if (i <= Math.Floor(Model.DiemTrungBinh))
                        {
                            <i class="fas fa-star text-warning"></i>
                        }
                        else if (i - 0.5 <= Model.DiemTrungBinh)
                        {
                            <i class="fas fa-star-half-alt text-warning"></i>
                        }
                        else
                        {
                            <i class="far fa-star text-warning"></i>
                        }
                    }
                </div>
                <div>từ @Model.TongSoDanhGia đánh giá</div>
            </div>
        </div>

        <div class="text-center my-3">
            <a asp-controller="DanhGia" asp-action="CreateDanhGia" asp-route-id="@Model.Phim.MaPhim" 
               class="btn btn-primary">
                <i class="fas fa-star me-1"></i> Viết đánh giá
            </a>
        </div>

        @if (Model.DanhSachDanhGia.Any())
        {
            <hr />
            <h6 class="mb-3">Đánh giá gần đây</h6>
            
            var danhGiaGanDay = Model.DanhSachDanhGia.Take(3);
            
            @foreach (var danhGia in danhGiaGanDay)
            {
                <div class="card mb-2">
                    <div class="card-body py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">@danhGia.NguoiDung.HoTen</h6>
                                <small class="text-muted">
                                    @danhGia.NgayDanhGia?.ToString("dd/MM/yyyy")
                                </small>
                            </div>
                            <div class="stars-container">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= danhGia.DiemSo)
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star text-warning"></i>
                                    }
                                }
                            </div>
                        </div>
                        <p class="mt-2 mb-0 text-truncate">@danhGia.BinhLuan</p>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="alert alert-info">
                Chưa có đánh giá nào. Hãy là người đầu tiên đánh giá phim này!
            </div>
        }
    </div>
</div>

<script>
    $(document).ready(function() {
        // Load FontAwesome if not already loaded
        if (!$('link[href*="fontawesome"]').length) {
            $('head').append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">');
        }
    });
</script> 