using CinemaBooking.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;

namespace CinemaBooking.Data
{
    public class ApplicationDbContext : IdentityDbContext<NguoiDung, IdentityRole<int>, int>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<VaiTro> VaiTros { get; set; }
        public DbSet<Phim> Phims { get; set; }
        public DbSet<NgonNguPhim> NgonNguPhims { get; set; }
        public DbSet<LichChieu> LichChieus { get; set; }
        public DbSet<PhongChieu> PhongChieus { get; set; }
        public DbSet<RapPhim> RapPhims { get; set; }
        public DbSet<Ghe> Ghes { get; set; }
        public DbSet<DatVe> DatVes { get; set; }
        public DbSet<DatVeGhe> DatVeGhes { get; set; }
        public DbSet<KhuyenMai> KhuyenMais { get; set; }
        public DbSet<ThanhToan> ThanhToans { get; set; }
        public DbSet<DanhGia> DanhGias { get; set; }
        public DbSet<LichSuGiaoDich> LichSuGiaoDiches { get; set; }
        public DbSet<OtpInfo> OtpInfos { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Mapping cho NguoiDung
            modelBuilder.Entity<NguoiDung>(entity =>
            {
                entity.ToTable("nguoi_dung");
                entity.Property(e => e.Id).HasColumnName("ma_nguoi_dung");
                entity.Property(e => e.UserName).HasColumnName("ten_dang_nhap");
                entity.Property(e => e.Email).HasColumnName("email");
                entity.Property(e => e.PasswordHash).HasColumnName("mat_khau");
                entity.Property(e => e.PhoneNumber).HasColumnName("so_dien_thoai");
                entity.Property(e => e.HoTen).HasColumnName("ho_ten");

                // Ignore các thuộc tính Identity không có trong database
                entity.Ignore(e => e.AccessFailedCount);
                entity.Ignore(e => e.ConcurrencyStamp);
                entity.Ignore(e => e.EmailConfirmed);
                entity.Ignore(e => e.LockoutEnabled);
                entity.Ignore(e => e.LockoutEnd);
                entity.Ignore(e => e.PhoneNumberConfirmed);
                entity.Ignore(e => e.SecurityStamp);
                entity.Ignore(e => e.TwoFactorEnabled);
                entity.Ignore(e => e.NormalizedUserName);
                entity.Ignore(e => e.NormalizedEmail);
            });

            // Thêm mapping cho OtpInfo
            modelBuilder.Entity<OtpInfo>(entity =>
            {
                entity.ToTable("otp_info");
                entity.Property(e => e.MaOtp).HasColumnName("ma_otp");
                entity.Property(e => e.Email).HasColumnName("email");
                entity.Property(e => e.MaXacThuc).HasColumnName("ma_xac_thuc");
                entity.Property(e => e.ThoiGianTao).HasColumnName("thoi_gian_tao");
                entity.Property(e => e.ThoiGianHetHan).HasColumnName("thoi_gian_het_han");
                entity.Property(e => e.LoaiOtp).HasColumnName("loai_otp");
                entity.Property(e => e.DaSuDung).HasColumnName("da_su_dung");
                entity.Property(e => e.MaNguoiDung).HasColumnName("ma_nguoi_dung");

                // Cấu hình quan hệ 1 - nhiều với NguoiDung nếu cần
                entity.HasOne<NguoiDung>()
                    .WithMany()
                    .HasForeignKey(e => e.MaNguoiDung)
                    .IsRequired(false);
            });

            // Mapping cho VaiTro
            modelBuilder.Entity<VaiTro>(entity =>
            {
                entity.ToTable("AspNetRoles");
                entity.Property(e => e.Id).HasColumnName("Id");
                entity.Property(e => e.Name).HasColumnName("Name");
                entity.Property(e => e.NormalizedName).HasColumnName("NormalizedName");
                entity.Property(e => e.ConcurrencyStamp).HasColumnName("ConcurrencyStamp");
                entity.Property(e => e.MoTa).HasColumnName("Description");
            });

            // Mapping cho IdentityUserRole
            modelBuilder.Entity<IdentityUserRole<int>>(entity =>
            {
                entity.ToTable("nguoi_dung_vai_tro");
                entity.Property(e => e.UserId).HasColumnName("ma_nguoi_dung");
                entity.Property(e => e.RoleId).HasColumnName("ma_vai_tro");
            });

            // Configure DatVeGhe composite key
            modelBuilder.Entity<DatVeGhe>()
                .HasKey(dvg => new { dvg.MaDatVe, dvg.MaGhe });

            // Configure DatVeGhe relationships
            modelBuilder.Entity<DatVeGhe>()
                .HasOne(dvg => dvg.DatVe)
                .WithMany(dv => dv.DatVeGhes)
                .HasForeignKey(dvg => dvg.MaDatVe)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DatVeGhe>()
                .HasOne(dvg => dvg.Ghe)
                .WithMany(g => g.DatVeGhes)
                .HasForeignKey(dvg => dvg.MaGhe)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure DanhGia
            modelBuilder.Entity<DanhGia>()
                .Property(d => d.BinhLuan)
                .IsUnicode(true)
                .HasColumnType("ntext");

            modelBuilder.Entity<DanhGia>()
                .HasOne(d => d.NguoiDung)
                .WithMany(n => n.DanhGias)
                .HasForeignKey(d => d.MaNguoiDung)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DanhGia>()
                .HasOne(d => d.Phim)
                .WithMany(p => p.DanhGias)
                .HasForeignKey(d => d.MaPhim)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DatVe>()
                .HasOne(d => d.NguoiDung)
                .WithMany(n => n.DatVes)
                .HasForeignKey(d => d.MaNguoiDung)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<LichSuGiaoDich>()
                .HasOne(l => l.NguoiDung)
                .WithMany(n => n.LichSuGiaoDichs)
                .HasForeignKey(l => l.MaNguoiDung)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<LichSuGiaoDich>()
                .HasOne(l => l.ThanhToan)
                .WithMany()
                .HasForeignKey(l => l.MaThanhToan)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure NgonNguPhim
            modelBuilder.Entity<NgonNguPhim>()
                .Property(n => n.MaPhim)
                .IsRequired(false);

            modelBuilder.Entity<NgonNguPhim>()
                .HasOne(n => n.Phim)
                .WithMany(p => p.NgonNguPhims)
                .HasForeignKey(n => n.MaPhim)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure LichChieu và NgonNguPhim
            modelBuilder.Entity<LichChieu>()
                .HasOne(l => l.NgonNguPhim)
                .WithMany(n => n.LichChieus)
                .HasForeignKey(l => l.MaNgonNgu)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}