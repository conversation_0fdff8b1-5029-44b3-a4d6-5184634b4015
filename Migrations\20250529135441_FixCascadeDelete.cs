﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CinemaBooking.Migrations
{
    /// <inheritdoc />
    public partial class FixCascadeDelete : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_dat_ve_ghe_ghe_ma_ghe",
                table: "dat_ve_ghe");

            migrationBuilder.AddForeignKey(
                name: "FK_dat_ve_ghe_ghe_ma_ghe",
                table: "dat_ve_ghe",
                column: "ma_ghe",
                principalTable: "ghe",
                principalColumn: "ma_ghe");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_dat_ve_ghe_ghe_ma_ghe",
                table: "dat_ve_ghe");

            migrationBuilder.AddForeignKey(
                name: "FK_dat_ve_ghe_ghe_ma_ghe",
                table: "dat_ve_ghe",
                column: "ma_ghe",
                principalTable: "ghe",
                principalColumn: "ma_ghe",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
