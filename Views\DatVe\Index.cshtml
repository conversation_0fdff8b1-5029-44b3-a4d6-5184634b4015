@model IEnumerable<CinemaBooking.Models.LichChieu>
@using System.Globalization

@{
    ViewData["Title"] = "Chọn lịch chiếu";
    var phim = ViewBag.Phim as CinemaBooking.Models.Phim;
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-4">
            <img src="@(string.IsNullOrEmpty(phim.UrlPoster) ? "/images/no-image.jpg" : phim.UrlPoster)" class="img-fluid rounded shadow" alt="@phim.TenPhim">
        </div>
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-body">
                    <h2 class="card-title mb-4">@phim.TenPhim</h2>
                    <div class="mb-3">
                        <span class="badge bg-primary">@phim.TheLoai</span>
                        <span class="badge bg-info">@phim.DinhDang</span>
                        <span class="badge bg-secondary">@phim.ThoiLuong phút</span>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="alert alert-warning">
                            Hiện tại chưa có lịch chiếu nào cho phim này. Vui lòng quay lại sau.
                        </div>
                    }
                    else
                    {
                        <h4>Chọn lịch chiếu:</h4>
                        <div class="list-group mt-3">
                            @foreach (var lichChieu in Model)
                            {
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="mb-1">@string.Format(CultureInfo.InvariantCulture, "{0:dd/MM/yyyy}", lichChieu.NgayChieu)</h5>
                                            <p class="mb-1">
                                                <strong>Giờ chiếu:</strong> @($"{lichChieu.GioChieu.Hours:D2}:{lichChieu.GioChieu.Minutes:D2}")
                                            </p>
                                            <small>
                                                <strong>Rạp:</strong> @lichChieu.PhongChieu.RapPhim.TenRap - 
                                                <strong>Phòng:</strong> @lichChieu.PhongChieu.SoPhong
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-success mb-2 d-block">@string.Format(CultureInfo.InvariantCulture, "{0:N0}", lichChieu.GiaVe) đ</span>
                                            <a href="@Url.Action("ChonGhe", "DatVe", new { maLichChieu = lichChieu.MaLichChieu })" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-chair me-1"></i>Chọn ghế
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }

                    <div class="mt-4">
                        <a asp-controller="Phim" asp-action="Detail" asp-route-id="@phim.MaPhim" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .list-group-item:hover {
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
        }
    </style>
} 