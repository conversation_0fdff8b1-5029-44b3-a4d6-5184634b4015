@model CinemaBooking.Models.ViewModels.RegisterViewModel

@{
    ViewData["Title"] = "Đăng ký";
    Layout = "~/Views/Shared/_Layout.cshtml";
    // Thay vì gán trực tiếp <PERSON>, sử dụng biến local
    var viewModel = Model ?? new CinemaBooking.Models.ViewModels.RegisterViewModel();
}

<div class="auth-section">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h4>Đăng ký tài khoản mới</h4>
                <p>Trở thành thành viên CineZore để nhận nhiều ưu đãi</p>
            </div>
            
            <div class="auth-body">
                @* Debug info *@
                @if (System.Diagnostics.Debugger.IsAttached || true)
                {
                    <div class="alert alert-secondary mb-3 d-none">
                        <small>Debug: BuocNhapOTP: @viewModel.BuocNhapOTP, DaGuiOTP: @viewModel.DaGuiOTP</small>
                    </div>
                }
                
                @if (TempData["OtpMessage"] != null)
                {
                    <div class="alert alert-info mb-3">
                        @TempData["OtpMessage"]
                    </div>
                }
                
                <form asp-action="Register" method="post" class="auth-form register">
                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger mb-3">
                            <div asp-validation-summary="ModelOnly" class="mb-0"></div>
                        </div>
                    }
                    
                    @if (viewModel.BuocNhapOTP)
                    {
                        <!-- Bước 2: Nhập OTP -->
                        <input type="hidden" asp-for="TenDangNhap" value="@viewModel.TenDangNhap" />
                        <input type="hidden" asp-for="Email" value="@viewModel.Email" />
                        <input type="hidden" asp-for="HoTen" value="@viewModel.HoTen" />
                        <input type="hidden" asp-for="SoDienThoai" value="@viewModel.SoDienThoai" />
                        <input type="hidden" asp-for="BuocNhapOTP" value="true" />
                        <input type="hidden" asp-for="DaGuiOTP" value="true" />
                        
                        <div class="mb-3">
                            <p class="text-center">Vui lòng nhập mã xác thực đã được gửi đến email <strong>@viewModel.Email</strong></p>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" asp-for="MaXacThuc" class="form-control" id="floatingOtp" placeholder=" " required />
                            <label for="floatingOtp">Mã xác thực</label>
                            <span asp-validation-for="MaXacThuc" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <a href="@Url.Action("ResendOtp", "Account", new { email = viewModel.Email, hoTen = viewModel.HoTen })" class="text-center d-block">
                                Gửi lại mã xác thực
                            </a>
                        </div>
                        
                        <button type="submit" class="btn-auth-submit">
                            Xác nhận đăng ký
                        </button>
                    }
                    else
                    {
                        <!-- Bước 1: Nhập thông tin đăng ký -->
                        <div class="form-row">
                            <div class="form-floating">
                                <input type="text" asp-for="TenDangNhap" class="form-control" id="floatingUsername" placeholder=" " required />
                                <label for="floatingUsername">Tên đăng nhập</label>
                                <span asp-validation-for="TenDangNhap" class="text-danger"></span>
                            </div>
                            
                            <div class="form-floating">
                                <input type="email" asp-for="Email" class="form-control" id="floatingEmail" placeholder=" " required />
                                <label for="floatingEmail">Email</label>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" asp-for="HoTen" class="form-control" id="floatingName" placeholder=" " required />
                            <label for="floatingName">Họ tên</label>
                            <span asp-validation-for="HoTen" class="text-danger"></span>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-floating">
                                <input type="password" asp-for="MatKhau" class="form-control" id="floatingPassword" placeholder=" " required />
                                <label for="floatingPassword">Mật khẩu</label>
                                <span asp-validation-for="MatKhau" class="text-danger"></span>
                            </div>
                            
                            <div class="form-floating">
                                <input type="password" asp-for="XacNhanMatKhau" class="form-control" id="floatingConfirmPassword" placeholder=" " required />
                                <label for="floatingConfirmPassword">Xác nhận mật khẩu</label>
                                <span asp-validation-for="XacNhanMatKhau" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="tel" asp-for="SoDienThoai" class="form-control" id="floatingPhone" placeholder=" " required />
                            <label for="floatingPhone">Số điện thoại</label>
                            <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                        </div>
                        
                        <button type="submit" class="btn-auth-submit">
                            Đăng ký
                        </button>
                    }
                </form>
            </div>
            <div class="auth-footer">
                <p>Đã có tài khoản? <a asp-action="Login">Đăng nhập ngay</a></p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 