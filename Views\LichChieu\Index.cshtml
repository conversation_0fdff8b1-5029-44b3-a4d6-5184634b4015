@model CinemaBooking.Models.ViewModels.LichChieuListViewModel

@{
    ViewData["Title"] = "Quản lý lịch chiếu";
}

<div class="container-fluid">
    <h1 class="mt-4">Quản lý lịch chiếu</h1>
    
    <div class="mb-3">
        <a asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm lịch chiếu mới
        </a>
    </div>
    
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }
    
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Bộ lọc
        </div>
        <div class="card-body">
            <form asp-action="Index" method="get">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">Tìm kiếm</label>
                        <input type="text" asp-for="SearchTerm" class="form-control" placeholder="Tên phim, tên rạp...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Phim</label>
                        <select asp-for="MaPhim" asp-items="Model.PhimList" class="form-select">
                            <option value="">-- Tất cả phim --</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Rạp</label>
                        <select asp-for="MaRap" asp-items="Model.RapList" class="form-select">
                            <option value="">-- Tất cả rạp --</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Ngày bắt đầu</label>
                        <input type="date" asp-for="NgayBatDau" class="form-control">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">Ngày kết thúc</label>
                        <input type="date" asp-for="NgayKetThuc" class="form-control">
                    </div>
                    <div class="col-md-9 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="fas fa-redo"></i> Đặt lại
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Danh sách lịch chiếu
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Phim</th>
                            <th>Rạp</th>
                            <th>Phòng</th>
                            <th>Ngày chiếu</th>
                            <th>Giờ chiếu</th>
                            <th>Thời lượng</th>
                            <th>Giá vé</th>
                            <th>Ngôn ngữ</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.LichChieus == null || !Model.LichChieus.Any())
                        {
                            <tr>
                                <td colspan="10" class="text-center">Không có lịch chiếu nào</td>
                            </tr>
                        }
                        else
                        {
                            @foreach (var item in Model.LichChieus)
                            {
                                <tr>
                                    <td>@item.MaLichChieu</td>
                                    <td>@item.Phim?.TenPhim</td>
                                    <td>@item.PhongChieu?.RapPhim?.TenRap</td>
                                    <td>@item.PhongChieu?.SoPhong</td>
                                    <td>@item.NgayChieu.ToString("dd/MM/yyyy")</td>
                                    <td>@item.GioChieu.ToString(@"hh\:mm")</td>
                                    <td>@item.Phim?.ThoiLuong phút</td>
                                    <td>@item.GiaVe.ToString("N0") VNĐ</td>
                                    <td>@(item.NgonNguPhim?.NgonNgu ?? "N/A")</td>
                                    <td>
                                        <div class="btn-group">
                                            <a asp-action="Edit" asp-route-id="@item.MaLichChieu" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.MaLichChieu" class="btn btn-sm btn-info">
                                                <i class="fas fa-info-circle"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.MaLichChieu" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Thêm datatable nếu cần
            if ($("table").length > 0) {
                $("table").DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Vietnamese.json'
                    },
                    "paging": true,
                    "pageLength": 10,
                    "order": [[4, 'asc'], [5, 'asc']], // Sắp xếp theo ngày và giờ chiếu
                    "dom": 'Bfrtip',
                    "buttons": [
                        'copy', 'excel', 'pdf', 'print'
                    ]
                });
            }
            
            // Load FontAwesome nếu chưa có
            if (!$('link[href*="fontawesome"]').length) {
                $('head').append('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">');
            }
        });
    </script>
} 