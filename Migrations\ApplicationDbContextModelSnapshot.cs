// <auto-generated />
using System;
using CinemaBooking.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CinemaBooking.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("CinemaBooking.Models.DanhGia", b =>
                {
                    b.Property<int>("MaDanhGia")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_danh_gia");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDanhGia"));

                    b.Property<string>("BinhLuan")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("ntext")
                        .HasColumnName("binh_luan");

                    b.Property<int?>("DiemSo")
                        .HasColumnType("int")
                        .HasColumnName("diem_so");

                    b.Property<int>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<int>("MaPhim")
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    b.Property<DateTime?>("NgayDanhGia")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_danh_gia");

                    b.HasKey("MaDanhGia");

                    b.HasIndex("MaNguoiDung");

                    b.HasIndex("MaPhim");

                    b.ToTable("danh_gia");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVe", b =>
                {
                    b.Property<int>("MaDatVe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_dat_ve");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDatVe"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ghi_chu");

                    b.Property<int?>("MaKhuyenMai")
                        .HasColumnType("int")
                        .HasColumnName("ma_khuyen_mai");

                    b.Property<int>("MaLichChieu")
                        .HasColumnType("int")
                        .HasColumnName("ma_lich_chieu");

                    b.Property<int>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<DateTime?>("NgayDat")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_dat");

                    b.Property<decimal>("TongTien")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("tong_tien");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("trang_thai");

                    b.HasKey("MaDatVe");

                    b.HasIndex("MaKhuyenMai");

                    b.HasIndex("MaLichChieu");

                    b.HasIndex("MaNguoiDung");

                    b.ToTable("dat_ve");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVeGhe", b =>
                {
                    b.Property<int>("MaDatVe")
                        .HasColumnType("int")
                        .HasColumnName("ma_dat_ve");

                    b.Property<int>("MaGhe")
                        .HasColumnType("int")
                        .HasColumnName("ma_ghe");

                    b.HasKey("MaDatVe", "MaGhe");

                    b.HasIndex("MaGhe");

                    b.ToTable("dat_ve_ghe");
                });

            modelBuilder.Entity("CinemaBooking.Models.Ghe", b =>
                {
                    b.Property<int>("MaGhe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_ghe");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaGhe"));

                    b.Property<string>("LoaiGhe")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("loai_ghe");

                    b.Property<int>("MaPhong")
                        .HasColumnType("int")
                        .HasColumnName("ma_phong");

                    b.Property<string>("SoGhe")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("so_ghe");

                    b.HasKey("MaGhe");

                    b.HasIndex("MaPhong");

                    b.ToTable("ghe");
                });

            modelBuilder.Entity("CinemaBooking.Models.KhuyenMai", b =>
                {
                    b.Property<int>("MaKhuyenMai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_khuyen_mai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaKhuyenMai"));

                    b.Property<string>("MaCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("ma_code");

                    b.Property<string>("MoTa")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime>("NgayBatDau")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_bat_dau");

                    b.Property<DateTime>("NgayKetThuc")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_ket_thuc");

                    b.Property<int>("PhanTramGiam")
                        .HasColumnType("int")
                        .HasColumnName("phan_tram_giam");

                    b.HasKey("MaKhuyenMai");

                    b.ToTable("khuyen_mai");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichChieu", b =>
                {
                    b.Property<int>("MaLichChieu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_lich_chieu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaLichChieu"));

                    b.Property<decimal>("GiaVe")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("gia_ve");

                    b.Property<TimeSpan>("GioChieu")
                        .HasColumnType("time")
                        .HasColumnName("gio_chieu");

                    b.Property<int?>("MaNgonNgu")
                        .HasColumnType("int")
                        .HasColumnName("ma_ngon_ngu");

                    b.Property<int>("MaPhim")
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    b.Property<int>("MaPhong")
                        .HasColumnType("int")
                        .HasColumnName("ma_phong");

                    b.Property<DateTime>("NgayChieu")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_chieu");

                    b.HasKey("MaLichChieu");

                    b.HasIndex("MaNgonNgu");

                    b.HasIndex("MaPhim");

                    b.HasIndex("MaPhong");

                    b.ToTable("lich_chieu");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichSuGiaoDich", b =>
                {
                    b.Property<int>("MaGiaoDich")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_giao_dich");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaGiaoDich"));

                    b.Property<string>("LoaiGiaoDich")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("loai_giao_dich");

                    b.Property<int?>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<int?>("MaThanhToan")
                        .HasColumnType("int")
                        .HasColumnName("ma_thanh_toan");

                    b.Property<DateTime?>("NgayGiaoDich")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_giao_dich");

                    b.Property<string>("NoiDung")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("noi_dung");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("trang_thai");

                    b.HasKey("MaGiaoDich");

                    b.HasIndex("MaNguoiDung");

                    b.HasIndex("MaThanhToan");

                    b.ToTable("lich_su_giao_dich");
                });

            modelBuilder.Entity("CinemaBooking.Models.NgonNguPhim", b =>
                {
                    b.Property<int>("MaNgonNgu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_ngon_ngu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaNgonNgu"));

                    b.Property<int?>("MaPhim")
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    b.Property<string>("NgonNgu")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ngon_ngu");

                    b.Property<string>("PhuDe")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("phu_de");

                    b.HasKey("MaNgonNgu");

                    b.HasIndex("MaPhim");

                    b.ToTable("ngon_ngu_phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.NguoiDung", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("email");

                    b.Property<string>("HoTen")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ho_ten");

                    b.Property<int?>("MaVaiTro")
                        .HasColumnType("int")
                        .HasColumnName("ma_vai_tro");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_tao");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mat_khau");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("so_dien_thoai");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("ten_dang_nhap");

                    b.HasKey("Id");

                    b.HasIndex("MaVaiTro");

                    b.ToTable("nguoi_dung", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.OtpInfo", b =>
                {
                    b.Property<int>("MaOtp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_otp");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaOtp"));

                    b.Property<bool>("DaSuDung")
                        .HasColumnType("bit")
                        .HasColumnName("da_su_dung");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("email");

                    b.Property<string>("LoaiOtp")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("loai_otp");

                    b.Property<int?>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<string>("MaXacThuc")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)")
                        .HasColumnName("ma_xac_thuc");

                    b.Property<DateTime>("ThoiGianHetHan")
                        .HasColumnType("datetime2")
                        .HasColumnName("thoi_gian_het_han");

                    b.Property<DateTime>("ThoiGianTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("thoi_gian_tao");

                    b.HasKey("MaOtp");

                    b.HasIndex("MaNguoiDung");

                    b.ToTable("otp_info", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.Phim", b =>
                {
                    b.Property<int>("MaPhim")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaPhim"));

                    b.Property<string>("DinhDang")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("dinh_dang");

                    b.Property<string>("MoTa")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime?>("NgayPhatHanh")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_phat_hanh");

                    b.Property<string>("TenPhim")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ten_phim");

                    b.Property<string>("TheLoai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("the_loai");

                    b.Property<int>("ThoiLuong")
                        .HasColumnType("int")
                        .HasColumnName("thoi_luong");

                    b.Property<string>("Trailer")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("trailer");

                    b.Property<string>("UrlPoster")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("url_poster");

                    b.HasKey("MaPhim");

                    b.ToTable("phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.PhongChieu", b =>
                {
                    b.Property<int>("MaPhong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_phong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaPhong"));

                    b.Property<int>("MaRap")
                        .HasColumnType("int")
                        .HasColumnName("ma_rap");

                    b.Property<int>("SoPhong")
                        .HasColumnType("int")
                        .HasColumnName("so_phong");

                    b.Property<int>("SucChua")
                        .HasColumnType("int")
                        .HasColumnName("suc_chua");

                    b.HasKey("MaPhong");

                    b.HasIndex("MaRap");

                    b.ToTable("phong_chieu");
                });

            modelBuilder.Entity("CinemaBooking.Models.RapPhim", b =>
                {
                    b.Property<int>("MaRap")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_rap");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaRap"));

                    b.Property<string>("DiaChi")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("dia_chi");

                    b.Property<string>("TenRap")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ten_rap");

                    b.Property<string>("ThanhPho")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("thanh_pho");

                    b.HasKey("MaRap");

                    b.ToTable("rap_phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.ThanhToan", b =>
                {
                    b.Property<int>("MaThanhToan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_thanh_toan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaThanhToan"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ghi_chu");

                    b.Property<int>("MaDatVe")
                        .HasColumnType("int")
                        .HasColumnName("ma_dat_ve");

                    b.Property<string>("MaGiaoDich")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ma_giao_dich");

                    b.Property<string>("MaGiaoDichNganHang")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ma_giao_dich_ngan_hang");

                    b.Property<DateTime?>("NgayThanhToan")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_thanh_toan");

                    b.Property<string>("PhuongThucThanhToan")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("phuong_thuc_thanh_toan");

                    b.Property<decimal>("SoTien")
                        .HasColumnType("decimal(10, 2)")
                        .HasColumnName("so_tien");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("trang_thai");

                    b.HasKey("MaThanhToan");

                    b.HasIndex("MaDatVe");

                    b.ToTable("thanh_toan");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("nvarchar(21)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("Name");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("NormalizedName");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);

                    b.HasDiscriminator().HasValue("IdentityRole<int>");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("ma_vai_tro");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("nguoi_dung_vai_tro", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.VaiTro", b =>
                {
                    b.HasBaseType("Microsoft.AspNetCore.Identity.IdentityRole<int>");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.ToTable("AspNetRoles", (string)null);

                    b.HasDiscriminator().HasValue("VaiTro");
                });

            modelBuilder.Entity("CinemaBooking.Models.DanhGia", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("DanhGias")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.Phim", "Phim")
                        .WithMany("DanhGias")
                        .HasForeignKey("MaPhim")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NguoiDung");

                    b.Navigation("Phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVe", b =>
                {
                    b.HasOne("CinemaBooking.Models.KhuyenMai", "KhuyenMai")
                        .WithMany("DatVes")
                        .HasForeignKey("MaKhuyenMai");

                    b.HasOne("CinemaBooking.Models.LichChieu", "LichChieu")
                        .WithMany("DatVes")
                        .HasForeignKey("MaLichChieu")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("DatVes")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("KhuyenMai");

                    b.Navigation("LichChieu");

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVeGhe", b =>
                {
                    b.HasOne("CinemaBooking.Models.DatVe", "DatVe")
                        .WithMany("DatVeGhes")
                        .HasForeignKey("MaDatVe")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.Ghe", "Ghe")
                        .WithMany("DatVeGhes")
                        .HasForeignKey("MaGhe")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DatVe");

                    b.Navigation("Ghe");
                });

            modelBuilder.Entity("CinemaBooking.Models.Ghe", b =>
                {
                    b.HasOne("CinemaBooking.Models.PhongChieu", "PhongChieu")
                        .WithMany("Ghes")
                        .HasForeignKey("MaPhong")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PhongChieu");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichChieu", b =>
                {
                    b.HasOne("CinemaBooking.Models.NgonNguPhim", "NgonNguPhim")
                        .WithMany("LichChieus")
                        .HasForeignKey("MaNgonNgu")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("CinemaBooking.Models.Phim", "Phim")
                        .WithMany("LichChieus")
                        .HasForeignKey("MaPhim")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.PhongChieu", "PhongChieu")
                        .WithMany("LichChieus")
                        .HasForeignKey("MaPhong")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NgonNguPhim");

                    b.Navigation("Phim");

                    b.Navigation("PhongChieu");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichSuGiaoDich", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("LichSuGiaoDichs")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CinemaBooking.Models.ThanhToan", "ThanhToan")
                        .WithMany()
                        .HasForeignKey("MaThanhToan")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("NguoiDung");

                    b.Navigation("ThanhToan");
                });

            modelBuilder.Entity("CinemaBooking.Models.NgonNguPhim", b =>
                {
                    b.HasOne("CinemaBooking.Models.Phim", "Phim")
                        .WithMany("NgonNguPhims")
                        .HasForeignKey("MaPhim")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.NguoiDung", b =>
                {
                    b.HasOne("CinemaBooking.Models.VaiTro", "VaiTro")
                        .WithMany()
                        .HasForeignKey("MaVaiTro");

                    b.Navigation("VaiTro");
                });

            modelBuilder.Entity("CinemaBooking.Models.OtpInfo", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", null)
                        .WithMany()
                        .HasForeignKey("MaNguoiDung");
                });

            modelBuilder.Entity("CinemaBooking.Models.PhongChieu", b =>
                {
                    b.HasOne("CinemaBooking.Models.RapPhim", "RapPhim")
                        .WithMany("PhongChieus")
                        .HasForeignKey("MaRap")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RapPhim");
                });

            modelBuilder.Entity("CinemaBooking.Models.ThanhToan", b =>
                {
                    b.HasOne("CinemaBooking.Models.DatVe", "DatVe")
                        .WithMany("ThanhToans")
                        .HasForeignKey("MaDatVe")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DatVe");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.NguoiDung", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVe", b =>
                {
                    b.Navigation("DatVeGhes");

                    b.Navigation("ThanhToans");
                });

            modelBuilder.Entity("CinemaBooking.Models.Ghe", b =>
                {
                    b.Navigation("DatVeGhes");
                });

            modelBuilder.Entity("CinemaBooking.Models.KhuyenMai", b =>
                {
                    b.Navigation("DatVes");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichChieu", b =>
                {
                    b.Navigation("DatVes");
                });

            modelBuilder.Entity("CinemaBooking.Models.NgonNguPhim", b =>
                {
                    b.Navigation("LichChieus");
                });

            modelBuilder.Entity("CinemaBooking.Models.NguoiDung", b =>
                {
                    b.Navigation("DanhGias");

                    b.Navigation("DatVes");

                    b.Navigation("LichSuGiaoDichs");
                });

            modelBuilder.Entity("CinemaBooking.Models.Phim", b =>
                {
                    b.Navigation("DanhGias");

                    b.Navigation("LichChieus");

                    b.Navigation("NgonNguPhims");
                });

            modelBuilder.Entity("CinemaBooking.Models.PhongChieu", b =>
                {
                    b.Navigation("Ghes");

                    b.Navigation("LichChieus");
                });

            modelBuilder.Entity("CinemaBooking.Models.RapPhim", b =>
                {
                    b.Navigation("PhongChieus");
                });
#pragma warning restore 612, 618
        }
    }
}
