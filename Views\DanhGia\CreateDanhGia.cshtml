@model CinemaBooking.Models.ViewModels.DanhGiaViewModel

@{
    ViewData["Title"] = "Đánh giá phim";
}

<div class="container">
    <div class="row mt-4">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <img src="@(string.IsNullOrEmpty(Model.UrlPoster) ? "/images/no-image.jpg" : Model.UrlPoster)" 
                                 class="img-fluid rounded" alt="@Model.TenPhim">
                        </div>
                        <div class="col-md-8">
                            <h3 class="mb-3">Đánh giá phim: @Model.TenPhim</h3>
                            
                            @if (TempData["DanhGiaMessage"] != null)
                            {
                                <div class="alert alert-info">
                                    @TempData["DanhGiaMessage"]
                                </div>
                            }
                            
                            <form asp-action="CreateDanhGia" method="post">
                                <input type="hidden" asp-for="MaPhim" />
                                <input type="hidden" asp-for="TenPhim" />
                                <input type="hidden" asp-for="UrlPoster" />
                                
                                <div class="mb-3">
                                    <label asp-for="DiemSo" class="form-label">Điểm số (1-5 sao)</label>
                                    <div class="rating">
                                        <input type="radio" asp-for="DiemSo" value="5" id="rating-5" />
                                        <label for="rating-5">5</label>
                                        <input type="radio" asp-for="DiemSo" value="4" id="rating-4" />
                                        <label for="rating-4">4</label>
                                        <input type="radio" asp-for="DiemSo" value="3" id="rating-3" />
                                        <label for="rating-3">3</label>
                                        <input type="radio" asp-for="DiemSo" value="2" id="rating-2" />
                                        <label for="rating-2">2</label>
                                        <input type="radio" asp-for="DiemSo" value="1" id="rating-1" />
                                        <label for="rating-1">1</label>
                                    </div>
                                    <span asp-validation-for="DiemSo" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="BinhLuan" class="form-label">Bình luận</label>
                                    <textarea asp-for="BinhLuan" class="form-control" rows="5" 
                                              placeholder="Chia sẻ cảm nhận của bạn về bộ phim này..."></textarea>
                                    <span asp-validation-for="BinhLuan" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">Gửi đánh giá</button>
                                    <a asp-controller="Phim" asp-action="Detail" asp-route-id="@Model.MaPhim" 
                                       class="btn btn-outline-secondary">Quay lại</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        margin-bottom: 15px;
    }
    
    .rating input {
        display: none;
    }
    
    .rating label {
        cursor: pointer;
        width: 40px;
        height: 40px;
        margin: 0 5px;
        background-color: #ddd;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        color: #666;
        transition: all 0.3s;
        font-weight: bold;
    }
    
    .rating input:checked ~ label,
    .rating input:hover ~ label {
        background-color: #ffc107;
        color: #000;
    }
</style>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 